import 'package:flutter/material.dart';
import '../controllers/contact_controller.dart';
import '../widgets/contact_card.dart';
import '../widgets/contact_form.dart';
import '../widgets/empty_state.dart';
import '../widgets/loading_widget.dart';
import '../widgets/user_profile_widget.dart';
import '../utils/app_constants.dart';
import '../utils/snackbar_helper.dart';

class HomePage extends StatefulWidget {
  final String title;

  const HomePage({
    super.key,
    required this.title,
  });

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late final ContactController _controller;

  @override
  void initState() {
    super.initState();
    _controller = ContactController();
    _initializeController();
  }

  Future<void> _initializeController() async {
    await _controller.initialize();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      title: Text(widget.title),
      actions: [
        const UserProfileWidget(),
        IconButton(
          onPressed: _handleRefresh,
          icon: const Icon(Icons.refresh),
          tooltip: 'Atualizar',
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildContactForm(),
          const SizedBox(height: AppConstants.defaultPadding),
          Expanded(
            child: _buildContactsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildContactForm() {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return ContactForm(
          controller: _controller.textController,
          onSubmit: _handleAddContact,
          isLoading: _controller.isLoading,
          errorMessage: _controller.errorMessage,
        );
      },
    );
  }

  Widget _buildContactsList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildContactsHeader(),
            const SizedBox(height: AppConstants.defaultPadding),
            Expanded(
              child: _buildContactsContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactsHeader() {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Lista de Contatos',
              style: AppConstants.titleTextStyle,
            ),
            Text(
              '${_controller.contactsCount} contatos',
              style: AppConstants.subtitleTextStyle,
            ),
          ],
        );
      },
    );
  }

  Widget _buildContactsContent() {
    return ListenableBuilder(
      listenable: _controller,
      builder: (context, child) {
        if (_controller.isLoading) {
          return const LoadingWidget();
        }

        if (_controller.errorMessage != null) {
          return EmptyState.error(
            errorMessage: _controller.errorMessage!,
            action: ElevatedButton.icon(
              onPressed: _handleRefresh,
              icon: const Icon(Icons.refresh),
              label: const Text('Tentar novamente'),
            ),
          );
        }

        if (!_controller.hasContacts) {
          return EmptyState.contacts();
        }

        return _buildContactsListView();
      },
    );
  }

  Widget _buildContactsListView() {
    return ListView.builder(
      itemCount: _controller.contacts.length,
      itemBuilder: (context, index) {
        final contact = _controller.contacts[index];
        return ContactCard(
          contact: contact,
          onDelete: () => _handleDeleteContact(contact.id),
          onTap: () => _showContactDetails(contact),
        );
      },
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _handleRefresh,
      tooltip: 'Atualizar',
      child: const Icon(Icons.refresh),
    );
  }

  // Handlers de eventos
  Future<void> _handleAddContact() async {
    final success = await _controller.addContact();
    
    if (!mounted) return;

    if (success) {
      SnackBarHelper.showSuccess(
        context,
        AppConstants.contactCreatedSuccess,
      );
    } else if (_controller.errorMessage != null) {
      SnackBarHelper.showError(
        context,
        _controller.errorMessage!,
      );
    }
  }

  Future<void> _handleDeleteContact(String contactId) async {
    final success = await _controller.deleteContact(contactId);
    
    if (!mounted) return;

    if (success) {
      SnackBarHelper.showSuccess(
        context,
        AppConstants.contactDeletedSuccess,
      );
    } else if (_controller.errorMessage != null) {
      SnackBarHelper.showError(
        context,
        _controller.errorMessage!,
      );
    }
  }

  Future<void> _handleRefresh() async {
    await _controller.refreshContacts();
    
    if (!mounted) return;

    if (_controller.errorMessage != null) {
      SnackBarHelper.showError(
        context,
        _controller.errorMessage!,
      );
    }
  }

  void _showContactDetails(contact) {
    // Implementar detalhes do contato se necessário
    SnackBarHelper.showInfo(
      context,
      'Detalhes do contato: ${contact.nome}',
    );
  }
}
