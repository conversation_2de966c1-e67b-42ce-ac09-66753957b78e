class AppUser {
  final String id;
  final String name;
  final String email;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  AppUser({
    required this.id,
    required this.name,
    required this.email,
    this.createdAt,
    this.updatedAt,
  });

  // Construtor para criar um usuário vazio
  AppUser.empty()
      : id = '',
        name = '',
        email = '',
        createdAt = null,
        updatedAt = null;

  // Construtor para criar um novo usuário
  AppUser.create({
    required this.name,
    required this.email,
  })  : id = '',
        createdAt = DateTime.now(),
        updatedAt = DateTime.now();

  // Método para converter de Map (Firestore) para AppUser
  factory AppUser.fromMap(Map<String, dynamic> map, String documentId) {
    return AppUser(
      id: documentId,
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      createdAt: map['createdAt']?.toDate(),
      updatedAt: map['updatedAt']?.toDate(),
    );
  }

  // Método para converter AppUser para Map (Firestore)
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // Método para criar uma cópia com alterações
  AppUser copyWith({
    String? id,
    String? name,
    String? email,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppUser(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Método para validar se o usuário é válido
  bool get isValid => name.trim().isNotEmpty && email.trim().isNotEmpty;

  // Método para obter as iniciais do nome
  String get initials {
    if (name.trim().isEmpty) return '?';
    
    List<String> words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[words.length - 1][0]}'.toUpperCase();
    }
  }

  @override
  String toString() {
    return 'AppUser{id: $id, name: $name, email: $email, createdAt: $createdAt, updatedAt: $updatedAt}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppUser && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
