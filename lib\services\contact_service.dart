import '../models/contact.dart';
import '../repositories/contact_repository.dart';

class ContactService {
  final ContactRepositoryInterface _repository;

  ContactService({
    ContactRepositoryInterface? repository,
  }) : _repository = repository ?? ContactRepository();

  // Buscar todos os contatos
  Future<List<Contact>> getAllContacts() async {
    try {
      return await _repository.getAllContacts();
    } catch (e) {
      throw ContactServiceException('Erro ao buscar contatos: $e');
    }
  }

  // Buscar contato por ID
  Future<Contact?> getContactById(String id) async {
    if (id.trim().isEmpty) {
      throw ContactServiceException('ID do contato não pode estar vazio');
    }

    try {
      return await _repository.getContactById(id);
    } catch (e) {
      throw ContactServiceException('Erro ao buscar contato: $e');
    }
  }

  // Criar novo contato
  Future<String> createContact(String nome) async {
    // Validações de negócio
    if (nome.trim().isEmpty) {
      throw ContactServiceException('Nome do contato não pode estar vazio');
    }

    if (nome.trim().length < 2) {
      throw ContactServiceException('Nome deve ter pelo menos 2 caracteres');
    }

    if (nome.trim().length > 100) {
      throw ContactServiceException('Nome não pode ter mais de 100 caracteres');
    }

    // Verificar se já existe um contato com o mesmo nome
    final existingContacts = await searchContactsByName(nome.trim());
    if (existingContacts.any((contact) => 
        contact.nome.toLowerCase() == nome.trim().toLowerCase())) {
      throw ContactServiceException('Já existe um contato com este nome');
    }

    try {
      final contact = Contact.create(nome: nome.trim());
      return await _repository.createContact(contact);
    } catch (e) {
      if (e is ContactServiceException) rethrow;
      throw ContactServiceException('Erro ao criar contato: $e');
    }
  }

  // Atualizar contato
  Future<void> updateContact(Contact contact) async {
    // Validações de negócio
    if (contact.id.trim().isEmpty) {
      throw ContactServiceException('ID do contato não pode estar vazio');
    }

    if (!contact.isValid) {
      throw ContactServiceException('Dados do contato são inválidos');
    }

    if (contact.nome.trim().length < 2) {
      throw ContactServiceException('Nome deve ter pelo menos 2 caracteres');
    }

    if (contact.nome.trim().length > 100) {
      throw ContactServiceException('Nome não pode ter mais de 100 caracteres');
    }

    try {
      final updatedContact = contact.copyWith(
        nome: contact.nome.trim(),
        updatedAt: DateTime.now(),
      );
      
      await _repository.updateContact(updatedContact);
    } catch (e) {
      if (e is ContactServiceException) rethrow;
      throw ContactServiceException('Erro ao atualizar contato: $e');
    }
  }

  // Deletar contato
  Future<void> deleteContact(String id) async {
    if (id.trim().isEmpty) {
      throw ContactServiceException('ID do contato não pode estar vazio');
    }

    // Verificar se o contato existe antes de deletar
    final contact = await getContactById(id);
    if (contact == null) {
      throw ContactServiceException('Contato não encontrado');
    }

    try {
      await _repository.deleteContact(id);
    } catch (e) {
      throw ContactServiceException('Erro ao deletar contato: $e');
    }
  }

  // Buscar contatos por nome
  Future<List<Contact>> searchContactsByName(String searchTerm) async {
    if (searchTerm.trim().isEmpty) {
      return await getAllContacts();
    }

    try {
      return await _repository.searchContactsByName(searchTerm.trim());
    } catch (e) {
      throw ContactServiceException('Erro ao buscar contatos: $e');
    }
  }

  // Observar mudanças nos contatos (Stream)
  Stream<List<Contact>> watchContacts() {
    try {
      return _repository.watchContacts();
    } catch (e) {
      throw ContactServiceException('Erro ao observar contatos: $e');
    }
  }

  // Obter estatísticas dos contatos
  Future<ContactStats> getContactStats() async {
    try {
      final contacts = await getAllContacts();
      final totalContacts = contacts.length;
      
      // Contar contatos criados hoje
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);
      final contactsToday = contacts.where((contact) {
        return contact.createdAt != null && 
               contact.createdAt!.isAfter(todayStart);
      }).length;

      return ContactStats(
        totalContacts: totalContacts,
        contactsCreatedToday: contactsToday,
      );
    } catch (e) {
      throw ContactServiceException('Erro ao obter estatísticas: $e');
    }
  }

  // Validar nome do contato
  bool isValidContactName(String nome) {
    final trimmedName = nome.trim();
    return trimmedName.isNotEmpty && 
           trimmedName.length >= 2 && 
           trimmedName.length <= 100;
  }
}

class ContactStats {
  final int totalContacts;
  final int contactsCreatedToday;

  ContactStats({
    required this.totalContacts,
    required this.contactsCreatedToday,
  });
}

class ContactServiceException implements Exception {
  final String message;
  
  ContactServiceException(this.message);
  
  @override
  String toString() => 'ContactServiceException: $message';
}
