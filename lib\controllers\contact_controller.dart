import 'package:flutter/material.dart';
import '../models/contact.dart';
import '../services/contact_service.dart';
import '../utils/app_constants.dart';

class ContactController extends ChangeNotifier {
  final ContactService _contactService;
  final TextEditingController textController = TextEditingController();

  // Estado da aplicação
  List<Contact> _contacts = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchTerm = '';

  ContactController({ContactService? contactService})
      : _contactService = contactService ?? ContactService();

  // Getters
  List<Contact> get contacts => _contacts;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchTerm => _searchTerm;
  bool get hasContacts => _contacts.isNotEmpty;
  int get contactsCount => _contacts.length;

  // Lista filtrada de contatos baseada na busca
  List<Contact> get filteredContacts {
    if (_searchTerm.isEmpty) {
      return _contacts;
    }
    
    return _contacts.where((contact) {
      return contact.nome.toLowerCase().contains(_searchTerm.toLowerCase());
    }).toList();
  }

  // Inicializar o controller
  Future<void> initialize() async {
    await refreshContacts();
  }

  // Carregar todos os contatos
  Future<void> refreshContacts() async {
    _setLoading(true);
    _clearError();

    try {
      _contacts = await _contactService.getAllContacts();
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar contatos: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Adicionar novo contato
  Future<bool> addContact() async {
    final nome = textController.text.trim();
    
    if (!_contactService.isValidContactName(nome)) {
      _setError(_getValidationError(nome));
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      await _contactService.createContact(nome);
      textController.clear();
      await refreshContacts();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Atualizar contato existente
  Future<bool> updateContact(Contact contact, String newName) async {
    if (!_contactService.isValidContactName(newName)) {
      _setError(_getValidationError(newName));
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      final updatedContact = contact.copyWith(
        nome: newName.trim(),
        updatedAt: DateTime.now(),
      );
      
      await _contactService.updateContact(updatedContact);
      await refreshContacts();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Deletar contato
  Future<bool> deleteContact(String contactId) async {
    _setLoading(true);
    _clearError();

    try {
      await _contactService.deleteContact(contactId);
      await refreshContacts();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Buscar contatos
  Future<void> searchContacts(String searchTerm) async {
    _searchTerm = searchTerm.trim();
    
    if (_searchTerm.isEmpty) {
      await refreshContacts();
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      _contacts = await _contactService.searchContactsByName(_searchTerm);
      notifyListeners();
    } catch (e) {
      _setError('Erro ao buscar contatos: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Limpar busca
  void clearSearch() {
    _searchTerm = '';
    refreshContacts();
  }

  // Obter estatísticas dos contatos
  Future<ContactStats?> getContactStats() async {
    try {
      return await _contactService.getContactStats();
    } catch (e) {
      _setError('Erro ao obter estatísticas: $e');
      return null;
    }
  }

  // Métodos privados para gerenciar estado
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Obter mensagem de erro de validação
  String _getValidationError(String nome) {
    if (nome.isEmpty) {
      return AppConstants.emptyNameError;
    } else if (nome.length < AppConstants.minContactNameLength) {
      return AppConstants.shortNameError;
    } else if (nome.length > AppConstants.maxContactNameLength) {
      return AppConstants.longNameError;
    }
    return AppConstants.genericError;
  }

  // Validar se o nome do contato é válido
  bool isValidContactName(String nome) {
    return _contactService.isValidContactName(nome);
  }

  // Limpar o controller de texto
  void clearTextController() {
    textController.clear();
  }

  @override
  void dispose() {
    textController.dispose();
    super.dispose();
  }
}
