import 'package:flutter/material.dart';
import '../utils/app_constants.dart';

class EmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Widget? action;
  final Color? iconColor;
  final double iconSize;

  const EmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.action,
    this.iconColor,
    this.iconSize = 64,
  });

  // Factory constructor para estado vazio de contatos
  factory EmptyState.contacts({Widget? action}) {
    return EmptyState(
      icon: Icons.contacts,
      title: AppConstants.emptyContactsTitle,
      subtitle: AppConstants.emptyContactsSubtitle,
      action: action,
    );
  }

  // Factory constructor para estado de busca sem resultados
  factory EmptyState.searchResults({required String searchTerm, Widget? action}) {
    return EmptyState(
      icon: Icons.search_off,
      title: 'Nenhum resultado encontrado',
      subtitle: 'Não encontramos contatos com "$searchTerm".\nTente buscar por outro termo.',
      action: action,
    );
  }

  // Factory constructor para estado de erro
  factory EmptyState.error({required String errorMessage, Widget? action}) {
    return EmptyState(
      icon: Icons.error_outline,
      title: 'Ops! Algo deu errado',
      subtitle: errorMessage,
      iconColor: AppConstants.errorColor,
      action: action,
    );
  }

  // Factory constructor para estado de loading
  factory EmptyState.loading({String? message}) {
    return EmptyState(
      icon: Icons.hourglass_empty,
      title: 'Carregando...',
      subtitle: message ?? 'Aguarde enquanto carregamos os dados.',
      iconColor: AppConstants.primaryColor,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildIcon(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildTitle(),
            const SizedBox(height: AppConstants.smallPadding),
            _buildSubtitle(),
            if (action != null) ...[
              const SizedBox(height: AppConstants.largePadding),
              action!,
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildIcon() {
    return Icon(
      icon,
      size: iconSize,
      color: iconColor ?? Colors.grey[400],
    );
  }

  Widget _buildTitle() {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Colors.grey,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildSubtitle() {
    return Text(
      subtitle,
      style: const TextStyle(
        fontSize: 14,
        color: Colors.grey,
        height: 1.4,
      ),
      textAlign: TextAlign.center,
    );
  }
}
