import 'package:flutter/material.dart';
import '../utils/app_constants.dart';

class ContactForm extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onSubmit;
  final bool isLoading;
  final String? errorMessage;
  final String buttonText;
  final String? hintText;
  final String? labelText;

  const ContactForm({
    super.key,
    required this.controller,
    required this.onSubmit,
    this.isLoading = false,
    this.errorMessage,
    this.buttonText = AppConstants.addContactButtonText,
    this.hintText = AppConstants.contactNameHint,
    this.labelText = AppConstants.contactNameLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTitle(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildTextField(),
            if (errorMessage != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              _buildErrorMessage(),
            ],
            const SizedBox(height: AppConstants.defaultPadding),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return const Text(
      'Enviar Dados',
      style: AppConstants.titleTextStyle,
    );
  }

  Widget _buildTextField() {
    return TextField(
      controller: controller,
      enabled: !isLoading,
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        border: const OutlineInputBorder(),
        prefixIcon: const Icon(Icons.person),
        errorText: null, // Erro será mostrado separadamente
        counterText: '${controller.text.length}/${AppConstants.maxContactNameLength}',
      ),
      maxLength: AppConstants.maxContactNameLength,
      maxLines: 1,
      textInputAction: TextInputAction.done,
      onSubmitted: (_) => _handleSubmit(),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: AppConstants.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(
          color: AppConstants.errorColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.error_outline,
            color: AppConstants.errorColor,
            size: AppConstants.smallIconSize,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Text(
              errorMessage!,
              style: const TextStyle(
                color: AppConstants.errorColor,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return ElevatedButton.icon(
      onPressed: isLoading ? null : _handleSubmit,
      icon: isLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : const Icon(Icons.person_add),
      label: Text(isLoading ? 'Adicionando...' : buttonText),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(
          vertical: 12,
          horizontal: AppConstants.defaultPadding,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        ),
        minimumSize: const Size(double.infinity, AppConstants.minButtonHeight),
      ),
    );
  }

  void _handleSubmit() {
    if (controller.text.trim().isEmpty) {
      return;
    }
    onSubmit();
  }
}
