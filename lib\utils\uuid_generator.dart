import 'package:uuid/uuid.dart';

class UuidGenerator {
  static final UuidGenerator _instance = UuidGenerator._internal();
  factory UuidGenerator() => _instance;
  UuidGenerator._internal();

  final Uuid _uuid = const Uuid();

  /// Gera um UUID v4 (aleatório)
  String generateV4() {
    return _uuid.v4();
  }

  /// Gera um UUID v1 (baseado em timestamp)
  String generateV1() {
    return _uuid.v1();
  }

  /// Gera um UUID v4 sem hífens
  String generateV4WithoutHyphens() {
    return _uuid.v4().replaceAll('-', '');
  }

  /// Valida se uma string é um UUID válido
  bool isValidUuid(String uuid) {
    try {
      return Uuid.isValidUUID(fromString: uuid);
    } catch (e) {
      return false;
    }
  }
}
