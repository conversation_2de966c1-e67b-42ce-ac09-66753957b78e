// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDueNjlIuBbv6BdvJx891nDiyJBHnXEMEM',
    appId: '1:930801861108:web:6264b0d4340ee0196f79a1',
    messagingSenderId: '930801861108',
    projectId: 'testebasico-a7f50',
    authDomain: 'testebasico-a7f50.firebaseapp.com',
    storageBucket: 'testebasico-a7f50.firebasestorage.app',
    measurementId: 'G-JWHXSG3G97',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC6hjhgmqwfta6wrOFGBu9QviNGnuPgSBw',
    appId: '1:930801861108:android:c926dc84799d87ea6f79a1',
    messagingSenderId: '930801861108',
    projectId: 'testebasico-a7f50',
    storageBucket: 'testebasico-a7f50.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCCQZXKNlpq5zi9WXU4rIyDV4caLLQoiUo',
    appId: '1:930801861108:ios:9cd28658a3a2736a6f79a1',
    messagingSenderId: '930801861108',
    projectId: 'testebasico-a7f50',
    storageBucket: 'testebasico-a7f50.firebasestorage.app',
    iosClientId: '930801861108-nacbijheog4p707h1defgfq34e6dbooj.apps.googleusercontent.com',
    iosBundleId: 'com.example.flutterBasico',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCCQZXKNlpq5zi9WXU4rIyDV4caLLQoiUo',
    appId: '1:930801861108:ios:9cd28658a3a2736a6f79a1',
    messagingSenderId: '930801861108',
    projectId: 'testebasico-a7f50',
    storageBucket: 'testebasico-a7f50.firebasestorage.app',
    iosClientId: '930801861108-nacbijheog4p707h1defgfq34e6dbooj.apps.googleusercontent.com',
    iosBundleId: 'com.example.flutterBasico',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDueNjlIuBbv6BdvJx891nDiyJBHnXEMEM',
    appId: '1:930801861108:web:6264b0d4340ee0196f79a1',
    messagingSenderId: '930801861108',
    projectId: 'testebasico-a7f50',
    authDomain: 'testebasico-a7f50.firebaseapp.com',
    storageBucket: 'testebasico-a7f50.firebasestorage.app',
    measurementId: 'G-JWHXSG3G97',
  );

}