import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/contact.dart';

abstract class ContactRepositoryInterface {
  Future<List<Contact>> getAllContacts();
  Future<Contact?> getContactById(String id);
  Future<String> createContact(Contact contact);
  Future<void> updateContact(Contact contact);
  Future<void> deleteContact(String id);
  Stream<List<Contact>> watchContacts();
  Future<List<Contact>> searchContactsByName(String searchTerm);
}

class ContactRepository implements ContactRepositoryInterface {
  final FirebaseFirestore _firestore;
  final String _collectionName = 'contatos';

  ContactRepository({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  CollectionReference get _collection => _firestore.collection(_collectionName);

  @override
  Future<List<Contact>> getAllContacts() async {
    try {
      final QuerySnapshot querySnapshot = await _collection
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Contact.fromMap(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
    } catch (e) {
      throw ContactRepositoryException('Erro ao buscar contatos: $e');
    }
  }

  @override
  Future<Contact?> getContactById(String id) async {
    try {
      final DocumentSnapshot doc = await _collection.doc(id).get();
      
      if (!doc.exists) {
        return null;
      }

      return Contact.fromMap(
        doc.data() as Map<String, dynamic>,
        doc.id,
      );
    } catch (e) {
      throw ContactRepositoryException('Erro ao buscar contato: $e');
    }
  }

  @override
  Future<String> createContact(Contact contact) async {
    try {
      final DocumentReference docRef = await _collection.add({
        ...contact.toMap(),
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return docRef.id;
    } catch (e) {
      throw ContactRepositoryException('Erro ao criar contato: $e');
    }
  }

  @override
  Future<void> updateContact(Contact contact) async {
    try {
      await _collection.doc(contact.id).update({
        ...contact.toMap(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw ContactRepositoryException('Erro ao atualizar contato: $e');
    }
  }

  @override
  Future<void> deleteContact(String id) async {
    try {
      await _collection.doc(id).delete();
    } catch (e) {
      throw ContactRepositoryException('Erro ao deletar contato: $e');
    }
  }

  @override
  Stream<List<Contact>> watchContacts() {
    try {
      return _collection
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((querySnapshot) {
        return querySnapshot.docs
            .map((doc) => Contact.fromMap(
                  doc.data() as Map<String, dynamic>,
                  doc.id,
                ))
            .toList();
      });
    } catch (e) {
      throw ContactRepositoryException('Erro ao observar contatos: $e');
    }
  }

  // Método para buscar contatos por nome
  @override
  Future<List<Contact>> searchContactsByName(String searchTerm) async {
    try {
      final QuerySnapshot querySnapshot = await _collection
          .where('nome', isGreaterThanOrEqualTo: searchTerm)
          .where('nome', isLessThanOrEqualTo: '$searchTerm\uf8ff')
          .orderBy('nome')
          .get();

      return querySnapshot.docs
          .map((doc) => Contact.fromMap(
                doc.data() as Map<String, dynamic>,
                doc.id,
              ))
          .toList();
    } catch (e) {
      throw ContactRepositoryException('Erro ao buscar contatos por nome: $e');
    }
  }

  // Método para contar total de contatos
  Future<int> getContactsCount() async {
    try {
      final QuerySnapshot querySnapshot = await _collection.get();
      return querySnapshot.docs.length;
    } catch (e) {
      throw ContactRepositoryException('Erro ao contar contatos: $e');
    }
  }
}

class ContactRepositoryException implements Exception {
  final String message;
  
  ContactRepositoryException(this.message);
  
  @override
  String toString() => 'ContactRepositoryException: $message';
}
