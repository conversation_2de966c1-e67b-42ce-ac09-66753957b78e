class Contact {
  final String id;
  final String nome;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Contact({
    required this.id,
    required this.nome,
    this.createdAt,
    this.updatedAt,
  });

  // Construtor para criar um contato vazio
  Contact.empty()
      : id = '',
        nome = '',
        createdAt = null,
        updatedAt = null;

  // Construtor para criar um novo contato (sem ID)
  Contact.create({
    required this.nome,
  })  : id = '',
        createdAt = DateTime.now(),
        updatedAt = DateTime.now();

  // Método para converter de Map (Firestore) para Contact
  factory Contact.fromMap(Map<String, dynamic> map, String documentId) {
    return Contact(
      id: documentId,
      nome: map['nome'] ?? '',
      createdAt: map['createdAt']?.toDate(),
      updatedAt: map['updatedAt']?.toDate(),
    );
  }

  // Método para converter Contact para Map (Firestore)
  Map<String, dynamic> toMap() {
    return {
      'nome': nome,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // Método para criar uma cópia com alterações
  Contact copyWith({
    String? id,
    String? nome,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Contact(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Método para validar se o contato é válido
  bool get isValid => nome.trim().isNotEmpty;

  // Método para obter as iniciais do nome
  String get initials {
    if (nome.trim().isEmpty) return '?';
    
    List<String> words = nome.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[words.length - 1][0]}'.toUpperCase();
    }
  }

  @override
  String toString() {
    return 'Contact{id: $id, nome: $nome, createdAt: $createdAt, updatedAt: $updatedAt}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Contact && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
