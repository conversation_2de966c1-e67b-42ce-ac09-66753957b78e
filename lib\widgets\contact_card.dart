import 'package:flutter/material.dart';
import '../models/contact.dart';
import '../utils/app_constants.dart';

class ContactCard extends StatelessWidget {
  final Contact contact;
  final VoidCallback? onDelete;
  final VoidCallback? onTap;
  final bool showDeleteButton;

  const ContactCard({
    super.key,
    required this.contact,
    this.onDelete,
    this.onTap,
    this.showDeleteButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
      ),
      child: ListTile(
        onTap: onTap,
        leading: _buildAvatar(),
        title: _buildTitle(),
        subtitle: _buildSubtitle(),
        trailing: showDeleteButton ? _buildDeleteButton(context) : null,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      backgroundColor: AppConstants.primaryColor,
      radius: 20,
      child: Text(
        contact.initials,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      contact.nome.isNotEmpty ? contact.nome : 'Nome não informado',
      style: const TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 16,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSubtitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ID: ${contact.id}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (contact.createdAt != null) ...[
          const SizedBox(height: 2),
          Text(
            'Criado em: ${_formatDate(contact.createdAt!)}',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[500],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDeleteButton(BuildContext context) {
    return IconButton(
      icon: const Icon(
        Icons.delete,
        color: AppConstants.errorColor,
        size: AppConstants.defaultIconSize,
      ),
      onPressed: onDelete != null
          ? () => _showDeleteConfirmation(context)
          : null,
      tooltip: 'Remover contato',
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(AppConstants.deleteConfirmationTitle),
          content: Text(
            '${AppConstants.deleteConfirmationMessage}\n\nContato: ${contact.nome}',
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(AppConstants.cancelButtonText),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onDelete?.call();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.errorColor,
                foregroundColor: Colors.white,
              ),
              child: const Text(AppConstants.confirmButtonText),
            ),
          ],
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year} '
        '${date.hour.toString().padLeft(2, '0')}:'
        '${date.minute.toString().padLeft(2, '0')}';
  }
}
